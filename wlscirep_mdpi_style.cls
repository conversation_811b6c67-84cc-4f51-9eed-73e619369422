% --- START OF FILE wlscirep_science_style.cls ---
%
% An unofficial LaTeX class for Scientific Report articles.
% MODIFIED to approximate Science journal style for fonts, layout, captions, and formatting.
% Based on wlscirep_mdpi_style.cls
% v1.0 : Initial adaptation from MDPI style
% v1.1 : Science journal style adaptation - Times font, 12pt, double spacing, US letter
% v1.2 : Science-style figure/table labels, abstract formatting, reference style
%
\NeedsTeXFormat{LaTeX2e}
\ProvidesClass{wlscirep_science_style}[2024/12/19, v1.2 Science Journal Style Adaptation]
\RequirePackage[utf8]{inputenc}
\RequirePackage[english]{babel}

\RequirePackage{ifthen}
\RequirePackage{calc}
\AtEndOfClass{\RequirePackage{microtype}}
\DeclareOption*{\PassOptionsToClass{\CurrentOption}{article}}
\ProcessOptions*
\newcommand{\JournalTitle}[1]{#1}
\LoadClass[12pt]{article} % Science uses 12pt text

% --- 字体设置 (Science 风格) ---
\RequirePackage{amsmath,amsfonts,amssymb}
% Science uses Times font. If you don't have this installed (most LaTeX installations will be
% fine) or prefer the old Computer Modern fonts, comment out the following line
\RequirePackage{newtxtext,newtxmath}
% Depending on your LaTeX fonts installation, you might get better results with one or both of these:
%\RequirePackage{mathptmx}
%\RequirePackage{txfonts}

% Double line spacing, including in captions
\linespread{1.5} % For some reason double spacing is 1.5, not 2.0!

% One space after each sentence
\frenchspacing

\RequirePackage{ifpdf}

\RequirePackage{graphicx,xcolor}
\RequirePackage{booktabs} % MDPI 也常用 booktabs

% --- 作者信息块设置 ---
\RequirePackage{authblk}
\setlength{\affilsep}{1em} 
\renewcommand\Authfont{\normalfont\bfseries\fontsize{11}{14}\selectfont} 
\renewcommand\Affilfont{\normalfont\fontsize{9}{11}\selectfont}     

% --- 页面几何布局 (Science 风格) ---
% Use US letter sized paper with 1 inch margins
\RequirePackage[letterpaper,margin=1in]{geometry}

% --- 图表标题设置 (Science 风格) ---
\RequirePackage{caption}
% Figure and Table labels in bold (Science style)
\makeatletter
\renewcommand{\fnum@figure}{\textbf{Figure \thefigure}}
\renewcommand{\fnum@table}{\textbf{Table \thetable}}
\makeatother

\captionsetup[figure]{%
    labelfont={bf}, % 粗体标签
    labelsep=period, % 标签后用句号
    textfont={normalsize}, % 正常大小文本
    justification=justified, % 两端对齐
    singlelinecheck=false,
    margin=10pt,
    aboveskip=6pt,
    belowskip=0pt
}
\captionsetup[table]{%
    position=top, % 表格标题在顶部
    labelfont={bf}, % 粗体标签
    labelsep=period,
    textfont={normalsize}, % 正常大小文本
    justification=justified,
    singlelinecheck=false,
    margin=10pt,
    aboveskip=6pt,
    belowskip=2pt % 表格标题与表格间的间距
}


% --- 参考文献设置 (Science 风格) ---
% Call the accompanying scicite.sty package.
% This formats citation numbers in Science style.
\RequirePackage{scicite}

% Reference section heading
\renewcommand\refname{References and Notes}

% Check if the style file exists, otherwise use a fallback
\IfFileExists{sciencemag.bst}
 {\bibliographystyle{sciencemag}} % Science journal style
 {\bibliographystyle{unsrt}} % Fallback if style not found

% --- 页眉页脚设置 ---
\RequirePackage{fancyhdr}
\RequirePackage{lastpage}
\pagestyle{fancy}
\fancyhf{} % 清空
\lhead{} 
\chead{} 
\cfoot{\footnotesize\thepage} % 
\rfoot{} 
\renewcommand{\headrulewidth}{0pt} 
\renewcommand{\footrulewidth}{0pt} 

% --- 行号设置 (MDPI 风格) ---
 %\RequirePackage{lineno} % <-- Commented out, lineno can sometimes interfere, enable only if needed

% --- 章节标题设置 (MODIFIED) ---
\RequirePackage[explicit]{titlesec}
\definecolor{mdpiTitleColor}{rgb}{0,0,0} 
\setcounter{secnumdepth}{3} % <<< FIX 1: Keep depth=3 for \ref, TOC, and bookmarks numbering to work! >>>

%%% <<< FIX START: Remove number display from section, subsection, subsubsection titles >>> %%%
% NOTE: The numbers still exist internally for \label/\ref and will appear in TOC and Bookmarks.
% To remove numbers from TOC, modify \titlecontents commands below (e.g., remove \contentslabel).

% SECTION: 12pt, Bold, Block
\titleformat{\section}
  {\normalfont\fontsize{12}{14}\selectfont\bfseries\color{mdpiTitleColor}} 
  %{\thesection.} % Original: Numbered format
  {} % FIX: No number displayed
  %{0.5em}     % Original: Space after number
  {0em}       % FIX: No space
  {#1}      
  []
% Define the style for \section* (numberless) - used for Acks, Refs etc.
\titleformat{name=\section,numberless} 
  {\normalfont\fontsize{12}{14}\selectfont\bfseries\color{mdpiTitleColor}} 
  {} % No number
  {0em}
  {#1}
  []  
\titlespacing*{\section}{0pt}{1.2\baselineskip}{0.6\baselineskip} 

% SUBSECTION: 10pt, Bold, Block
\titleformat{\subsection}
  {\normalfont\fontsize{10}{12}\selectfont\bfseries\color{mdpiTitleColor}} 
  %{\thesubsection.} % Original
  {} % FIX: No number displayed
  %{0.5em} % Original
   {0em} % FIX: No space
  {#1}
  []
\titlespacing*{\subsection}{0pt}{1.0\baselineskip}{0.5\baselineskip} 

% SUBSUBSECTION: 10pt, BOLD+ITALIC, RUN-IN 
\titleformat{\subsubsection}[runin] 
   {\normalfont\fontsize{10}{12}\selectfont\bfseries\itshape\color{mdpiTitleColor}} % bfseries + itshape
  %{\thesubsubsection.} % Original
   {} % FIX: No number displayed
  %{0.5em} % Original: 编号与标题间距
  {0em} % FIX: No space
  {#1} % 标题文本
  [.\ ] % 标题后加点和空格，与正文隔开
\titlespacing*{\subsubsection}{0pt}{0.8\baselineskip}{0em} % 
%%% <<< FIX END >>> %%%


\titleformat{\paragraph}[runin] % MDPI paragraph 通常是 runin
  {\normalfont\normalsize\bfseries} % MDPI paragraph 标题加粗
  {} % 通常无编号
  {0em}
  {#1} 
  [.\ ] 
\titlespacing*{\paragraph}{0pt}{0.8\baselineskip}{0em} 

% tableofcontents set-up
% NOTE: These commands control TOC display. Numbers will appear here unless \contentslabel is removed/modified.
\usepackage{titletoc}
\contentsmargin{0cm}
\titlecontents{section}[\tocsep]
  {\addvspace{4pt}\normalfont\small\bfseries} 
  {\contentslabel[\thecontentslabel]{\tocsep}}
  {}
  {\hfill\normalfont\thecontentspage}
  []
\titlecontents{subsection}[\tocsep]
  {\addvspace{2pt}\normalfont\small} 
  {\contentslabel[\thecontentslabel]{\tocsep}}
  {}
  {\ \titlerule*[.5pc]{.}\ \normalfont\thecontentspage}
  []
\titlecontents*{subsubsection}[\tocsep]
  {\normalfont\footnotesize} 
  {}
  {}
  {}
  [\ \textbullet\ ]  
  
\RequirePackage{enumitem}

% article meta data
\newcommand{\keywords}[1]{\def\@keywords{#1}}

% abstract 定义 (Science 风格)
% Abstract formatting and spacing - no heading
\renewenvironment{abstract}
	{\quotation}
	{\endquotation}

\def\xabstract{abstract}
\long\def\abstract#1\end#2{%
    \def\two{#2}%
    \ifx\two\xabstract
        \long\gdef\theabstract{\ignorespaces#1}% 移除 \noindent
        \def\go{\end{abstract}}%
    \else
        \typeout{^^J^^J PLEASE DO NOT USE ANY \string\begin\space \string\end^^J COMMANDS WITHIN ABSTRACT^^J^^J}#1\end{#2}%
        \gdef\theabstract{\vskip12pt BADLY FORMED ABSTRACT: PLEASE DO NOT USE {\tt\string\begin...\string\end} COMMANDS WITHIN THE ABSTRACT\vskip12pt}\let\go\relax
    \fi
    \go}

% custom title page (Science 风格)
\renewcommand{\@maketitle}{%
     \thispagestyle{fancy}%

        % --- Title Block (Science style) ---
         \begingroup % Local group for title style
         \raggedright % Apply left-align only to title
        {\normalfont\fontsize{16}{18}\selectfont\bfseries\boldmath \@title \par} % Science style title
        \endgroup

        \vspace{2.5ex} % Space between title and authors

        % --- Author Block ---
         \begingroup % Local group for author style
         \raggedright % Apply left-align only to author block
        {\normalfont\fontsize{11}{14}\selectfont \<AUTHOR> % 作者
        \endgroup

         \vspace{1.5ex} % Space between authors/affiliations and abstract

        % --- Abstract Block (Science style - no heading, in bold) ---
         \ifdefined\theabstract
            \begin{abstract}
            \bfseries\boldmath\theabstract
            \end{abstract}
            \par\vspace{1ex}%
        \fi

        % --- Keywords Block ---
        \ifdefined\@keywords
            \noindent
             {\normalfont\small\bfseries Keywords:~}\begingroup\normalfont\small\@keywords\endgroup
            \par\vspace{1ex}%
        \fi

        \vspace{0.8ex} % Space before main text starts
    \par
}
%-----------------------------------------------
\setlength{\columnsep}{0.55cm} 
\definecolor{color1}{RGB}{0,0,0} 
\newcommand{\keywordname}{Keywords:} 
\newlength{\tocsep} 
\setlength\tocsep{1.5pc} 
% FIX Bookmark: Ensure sections, subsections, subsubsections are included
\setcounter{tocdepth}{3} 

%-----------------------------------------------
% FIX BOOKMARK: Remove the \addcontentsline from here, it is now handled in main-tex.txt
% with \phantomsection\addcontentsline{toc}{section}{References} before \bibliography
\let\oldbibliography\thebibliography
\renewcommand{\thebibliography}[1]{%
%\ifthenelse{\isundefined{\contentsline}}{}{% % <<<--- FIX: COMMENT OUT
%    \addcontentsline{toc}{section}{\hspace*{-\tocsep}\refname}% % <<<--- FIX: COMMENT OUT
%}% % <<<--- FIX: COMMENT OUT
\oldbibliography{#1}%
\setlength\itemsep{0pt}% 
}

% hyperref must be loaded late. Add bookmark options. Must be AFTER titlesec
% bookmarksnumbered=true ensures numbers appear in PDF bookmarks even if not in headings
%%% <<< FIX: Remove numbers from PDF Bookmarks >>> %%%
\RequirePackage[bookmarks=true, bookmarksnumbered=false, colorlinks=true, allcolors=blue, pdfstartview=FitH]{hyperref} 

% 确保在文档开始时启用行号 (如果需要全局行号)
 % \AtBeginDocument{\linenumbers} % <-- enable only if needed

\endinput
%--- END OF FILE wlscirep_science_style.cls ---